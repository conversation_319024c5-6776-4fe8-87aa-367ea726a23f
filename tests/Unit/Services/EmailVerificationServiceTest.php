<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\EmailVerificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

final class EmailVerificationServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailVerificationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(EmailVerificationService::class);
        Cache::flush(); // Clean cache before each test
    }

    protected function tearDown(): void
    {
        Cache::flush(); // Clean cache after each test
        parent::tearDown();
    }

    public function test_can_send_verification_code(): void
    {
        $email = '<EMAIL>';
        
        $code = $this->service->sendVerificationCode($email);
        
        $this->assertIsString($code);
        $this->assertEquals(6, strlen($code));
        $this->assertMatchesRegularExpression('/^\d{6}$/', $code);
        
        // Verify code is cached
        $this->assertTrue($this->service->hasValidCode($email));
    }

    public function test_can_verify_correct_code(): void
    {
        $email = '<EMAIL>';
        $code = $this->service->sendVerificationCode($email);
        
        $result = $this->service->verifyCode($email, $code);
        
        $this->assertTrue($result);
        
        // Code should be removed after successful verification
        $this->assertFalse($this->service->hasValidCode($email));
    }

    public function test_cannot_verify_incorrect_code(): void
    {
        $email = '<EMAIL>';
        $this->service->sendVerificationCode($email);
        
        $result = $this->service->verifyCode($email, '999999');
        
        $this->assertFalse($result);
        
        // Code should still exist after failed verification
        $this->assertTrue($this->service->hasValidCode($email));
    }

    public function test_cannot_verify_non_existent_code(): void
    {
        $email = '<EMAIL>';
        
        $result = $this->service->verifyCode($email, '123456');
        
        $this->assertFalse($result);
    }

    public function test_has_valid_code_returns_false_for_non_existent_code(): void
    {
        $email = '<EMAIL>';
        
        $result = $this->service->hasValidCode($email);
        
        $this->assertFalse($result);
    }

    public function test_get_code_ttl_returns_correct_value(): void
    {
        $email = '<EMAIL>';
        $this->service->sendVerificationCode($email);
        
        $ttl = $this->service->getCodeTTL($email);
        
        $this->assertGreaterThan(0, $ttl);
        $this->assertLessThanOrEqual(900, $ttl); // Should be <= 15 minutes
    }

    public function test_get_code_ttl_returns_negative_for_non_existent_code(): void
    {
        $email = '<EMAIL>';
        
        $ttl = $this->service->getCodeTTL($email);
        
        $this->assertEquals(-2, $ttl); // Redis returns -2 for non-existent keys
    }

    public function test_verification_codes_are_unique(): void
    {
        $email1 = '<EMAIL>';
        $email2 = '<EMAIL>';
        
        $code1 = $this->service->sendVerificationCode($email1);
        $code2 = $this->service->sendVerificationCode($email2);
        
        // While codes could theoretically be the same (1 in 1,000,000 chance),
        // in practice they should be different
        $this->assertNotEquals($code1, $code2);
        
        // Both should be valid
        $this->assertTrue($this->service->hasValidCode($email1));
        $this->assertTrue($this->service->hasValidCode($email2));
    }

    public function test_can_overwrite_existing_code(): void
    {
        $email = '<EMAIL>';
        
        $code1 = $this->service->sendVerificationCode($email);
        $code2 = $this->service->sendVerificationCode($email);
        
        // First code should no longer be valid
        $this->assertFalse($this->service->verifyCode($email, $code1));
        
        // Second code should be valid
        $this->assertTrue($this->service->verifyCode($email, $code2));
    }

    public function test_verification_code_format(): void
    {
        $email = '<EMAIL>';
        
        // Generate multiple codes to test format consistency
        for ($i = 0; $i < 10; $i++) {
            $code = $this->service->sendVerificationCode($email);
            
            $this->assertEquals(6, strlen($code));
            $this->assertMatchesRegularExpression('/^\d{6}$/', $code);
            $this->assertGreaterThanOrEqual(0, (int) $code);
            $this->assertLessThanOrEqual(999999, (int) $code);
        }
    }
}
